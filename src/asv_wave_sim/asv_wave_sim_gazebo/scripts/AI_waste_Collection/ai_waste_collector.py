#!/usr/bin/env python3

import rospy
import math
import time
from asv_wave_sim_gazebo.msg import WasteDetection
from gazebo_msgs.srv import ApplyBodyWrench, ApplyBodyWrenchRequest, BodyRequest

class ProfessionalWasteCollector:
    def __init__(self):
        rospy.init_node('professional_waste_collector', anonymous=True)

        # Wait for Gazebo services
        try:
            rospy.wait_for_service('/gazebo/apply_body_wrench', timeout=10)
            rospy.wait_for_service('/gazebo/clear_body_wrenches', timeout=10)
        except rospy.ROSException as e:
            rospy.logerr(f"Gazebo services not available: {e}")
            return

        self.apply_wrench = rospy.ServiceProxy('/gazebo/apply_body_wrench', ApplyBodyWrench)
        self.clear_wrench = rospy.ServiceProxy('/gazebo/clear_body_wrenches', BodyRequest)

        # PARAMÈTRES PID PRÉCIS POUR LA RÉGULATION CONTINUE
        self.kp_angular = 0.8    # Gain proportionnel ÉLEVÉ pour rotation précise
        self.ki_angular = 0.1    # Gain intégral pour éliminer l'erreur statique
        self.kd_angular = 0.15   # Gain dérivé pour stabilité et précision

        self.kp_linear = 0.4     # Gain proportionnel ÉLEVÉ pour avancement précis
        self.ki_linear = 0.05    # Gain intégral pour éliminer l'erreur statique
        self.kd_linear = 0.08    # Gain dérivé pour stabilité et précision

        # VITESSES POUR ALLER VERS LE DÉCHET
        self.search_speed = 0.4          # Vitesse de recherche (aucun déchet)
        self.approach_speed = 0.15       # Vitesse LENTE pour aller vers le déchet
        self.collection_speed = 0.2      # Vitesse pour collecte finale
        self.regulation_speed = 0.3      # Vitesse de référence pour la régulation

        # SEUILS DE PRÉCISION pour la régulation
        self.centering_threshold = 0.01  # Seuil TRÈS STRICT pour le centrage précis
        self.min_detection_ratio = 0.05  # Ratio minimum pour considérer une détection significative
        self.precision_threshold = 0.02  # Seuil de précision pour les micro-ajustements
        self.fine_tuning_threshold = 0.005  # Seuil ultra-précis pour les ajustements fins
        self.upper_centering_threshold = 0.02  # Seuil très strict pour le centrage dans Upper Middle
        self.upper_centering_time = 0.0  # Temps passé en phase de centrage Upper Middle
        self.min_upper_centering_time = 3.0  # Temps minimum augmenté pour une meilleure stabilisation
        self.min_upper_detection_ratio = 0.8  # Ratio minimum de détection dans Upper Middle (80%)

        # Limites de commande
        self.max_torque = 0.20
        self.min_torque = -0.20
        self.base_speed = -0.30

        # Variables d'état du régulateur
        self.angular_error_integral = 0.0
        self.linear_error_integral = 0.0
        self.previous_angular_error = 0.0
        self.previous_linear_error = 0.0
        self.previous_time = time.time()

        # Identification des ventilateurs
        self.fan_right = "boatcleaningc::fandroit"
        self.fan_left = "boatcleaningc::fangauche"

        # État du système
        self.collection_count = 0
        self.regulation_mode = "SEARCH"  # SEARCH, ORIENT, APPROACH, COLLECT
        self.target_position = {"x": 0, "y": 0}  # Position cible du déchet
        self.regulation_active = False

        # État de navigation : 'search' ou 'collection'
        self.navigation_phase = 'search'  # Phase initiale : recherche

        # Variables manquantes pour compatibilité
        self.strong_turn = -0.15
        self.medium_turn = -0.10
        self.forward_speed = -0.08

        # Seuils de précision pour la détection
        self.precision_threshold = 0.05  # Seuil de précision pour le centrage
        self.approach_threshold = 0.1    # Seuil pour l'approche progressive

        # Subscribe to waste detection
        rospy.Subscriber('/waste_detection', WasteDetection, self.waste_detection_callback)
        rospy.loginfo("Professional Waste Collector with PID Regulation started!")

    def apply_torque(self, link_name, torque):
        try:
            self.clear_wrench(link_name)
            if abs(torque) > 0.001:
                req = ApplyBodyWrenchRequest()
                req.body_name = link_name
                req.reference_frame = "world"
                req.wrench.torque.x = torque
                req.duration = rospy.Duration(-1)
                self.apply_wrench(req)
                rospy.loginfo(f"Applied torque {torque} on {link_name}")
            else:
                rospy.loginfo(f"Cleared torque on {link_name}")
        except rospy.ServiceException as e:
            rospy.logerr(f"Failed to apply torque on {link_name}: {e}")

    def stop_fans(self):
        self.apply_torque(self.fan_right, 0.0)
        self.apply_torque(self.fan_left, 0.0)
        rospy.loginfo("Boat stopped.")

    def move_forward_slowly(self):
        """Move forward slowly for search and approach"""
        self.apply_torque(self.fan_right, self.base_speed)
        self.apply_torque(self.fan_left, self.base_speed)
        rospy.loginfo("Boat moving forward slowly.")

    def calculate_waste_position_error(self, left_count, right_count, middle_count, bottom_middle_count, upper_left_count=0, upper_right_count=0):
        """
        🎯 STRATÉGIE SIMPLE : ALLER VERS LE DÉCHET AVEC RÉGULATION
        Phase 1: Recherche quand aucun déchet
        Phase 2: Aller vers le déchet avec petite vitesse + régulation pour guider vers Middle puis Bottom Middle

        6 ZONES: Left, Right, Upper left, Middle, Upper right, Bottom middle
        """
        # Déterminer si un déchet est détecté
        waste_detected = (left_count > 0 or right_count > 0 or middle_count > 0 or bottom_middle_count > 0 or
                         upper_left_count > 0 or upper_right_count > 0)

        # 🎯 PRIORITÉ 1: DÉCHET EN BOTTOM MIDDLE - COLLECTE FINALE
        if bottom_middle_count > 0:
            angular_error = 0.0
            linear_error = self.collection_speed  # Vitesse pour collecte finale
            rospy.loginfo("🎯 COLLECTE: Déchet en Bottom Middle - COLLECTE FINALE!")
            return angular_error, linear_error

        # 🎯 PRIORITÉ 2: DÉCHET EN MIDDLE - POUSSER VERS BOTTOM MIDDLE
        if middle_count > 0:
            angular_error = 0.0  # Maintenir le cap droit
            linear_error = self.approach_speed  # Vitesse lente pour pousser vers Bottom Middle
            rospy.loginfo("🎯 MIDDLE: Déchet en Middle - poussée vers Bottom Middle")
            return angular_error, linear_error

        # 🎯 DÉCHET DÉTECTÉ - ALLER VERS LUI AVEC RÉGULATION PRÉCISE
        if waste_detected:
            rospy.loginfo("🎯 COLLECTE: Déchet détecté - aller vers lui avec régulation PRÉCISE!")

            # Calculer les ratios pour une régulation précise
            total_detections = left_count + right_count + upper_left_count + upper_right_count + middle_count + bottom_middle_count

            if total_detections > 0:
                left_ratio = (left_count + upper_left_count) / total_detections
                right_ratio = (right_count + upper_right_count) / total_detections

                # Calcul de l'erreur de centrage PRÉCISE
                center_error = left_ratio - right_ratio

                # Guider le déchet vers Middle avec régulation PRÉCISE
                if left_count > 0 or upper_left_count > 0:
                    # Déchet à GAUCHE - Rotation PRÉCISE à gauche pour le guider vers Middle
                    if abs(center_error) > self.precision_threshold:
                        angular_error = 0.15 * center_error  # Correction proportionnelle PRÉCISE
                        rospy.loginfo(f"🔄 GUIDAGE PRÉCIS: Déchet à gauche - erreur={center_error:.3f}, correction={angular_error:.3f}")
                    else:
                        angular_error = 0.05 * center_error  # Micro-correction ultra-précise
                        rospy.loginfo(f"🔄 MICRO-AJUSTEMENT: Déchet à gauche - erreur={center_error:.3f}, micro-correction={angular_error:.3f}")

                    linear_error = self.approach_speed  # Vitesse lente

                elif right_count > 0 or upper_right_count > 0:
                    # Déchet à DROITE - Rotation PRÉCISE à droite pour le guider vers Middle
                    if abs(center_error) > self.precision_threshold:
                        angular_error = 0.15 * center_error  # Correction proportionnelle PRÉCISE
                        rospy.loginfo(f"🔄 GUIDAGE PRÉCIS: Déchet à droite - erreur={center_error:.3f}, correction={angular_error:.3f}")
                    else:
                        angular_error = 0.05 * center_error  # Micro-correction ultra-précise
                        rospy.loginfo(f"🔄 MICRO-AJUSTEMENT: Déchet à droite - erreur={center_error:.3f}, micro-correction={angular_error:.3f}")

                    linear_error = self.approach_speed  # Vitesse lente

                else:
                    # Sécurité - avancement lent avec précision
                    angular_error = 0.0
                    linear_error = self.approach_speed
                    rospy.loginfo("🔄 APPROCHE PRÉCISE: Avancement vers le déchet")
            else:
                # Sécurité - pas de détections valides
                angular_error = 0.0
                linear_error = self.approach_speed
                rospy.loginfo("⚠️ SÉCURITÉ: Aucune détection valide - avancement lent")

            return angular_error, linear_error

        # 🔍 AUCUN DÉCHET - RECHERCHE
        angular_error = 0.0  # Maintenir le cap droit
        linear_error = self.search_speed  # Vitesse de recherche
        rospy.loginfo(f"🔍 RECHERCHE: Avancement à {self.search_speed} - aucun déchet détecté")

        return angular_error, linear_error

    def pid_controller(self, angular_error, linear_error):
        """
        RÉGULATEUR PID PRÉCIS ET CONTINU
        Calcule les commandes moteur basées sur les erreurs avec régulation haute précision
        """
        current_time = time.time()
        dt = current_time - self.previous_time

        if dt <= 0:
            dt = 0.01  # Éviter division par zéro

        # === RÉGULATION ANGULAIRE PRÉCISE ===
        # Terme proportionnel PRÉCIS
        angular_p = self.kp_angular * angular_error

        # Terme intégral avec limitation PRÉCISE pour éviter le windup
        self.angular_error_integral += angular_error * dt
        # Limitation plus stricte pour plus de précision
        self.angular_error_integral = max(min(self.angular_error_integral, 0.5), -0.5)
        angular_i = self.ki_angular * self.angular_error_integral

        # Terme dérivé avec filtrage pour réduire le bruit
        angular_derivative = (angular_error - self.previous_angular_error) / dt
        # Filtrage du terme dérivé pour plus de stabilité
        if abs(angular_derivative) > 10.0:  # Limite le terme dérivé
            angular_derivative = 10.0 if angular_derivative > 0 else -10.0
        angular_d = self.kd_angular * angular_derivative

        # Commande angulaire totale PRÉCISE
        angular_command = angular_p + angular_i + angular_d

        # === RÉGULATION LINÉAIRE PRÉCISE ===
        # Terme proportionnel PRÉCIS
        linear_p = self.kp_linear * linear_error

        # Terme intégral avec limitation PRÉCISE
        self.linear_error_integral += linear_error * dt
        # Limitation plus stricte pour plus de précision
        self.linear_error_integral = max(min(self.linear_error_integral, 0.5), -0.5)
        linear_i = self.ki_linear * self.linear_error_integral

        # Terme dérivé avec filtrage
        linear_derivative = (linear_error - self.previous_linear_error) / dt
        # Filtrage du terme dérivé pour plus de stabilité
        if abs(linear_derivative) > 5.0:  # Limite le terme dérivé
            linear_derivative = 5.0 if linear_derivative > 0 else -5.0
        linear_d = self.kd_linear * linear_derivative

        # Commande linéaire totale PRÉCISE
        linear_command = linear_p + linear_i + linear_d

        # Saturation PRÉCISE des commandes
        angular_command = max(min(angular_command, self.max_torque), self.min_torque)
        linear_command = max(min(linear_command, self.max_torque), self.min_torque)

        # Mise à jour des variables d'état
        self.previous_angular_error = angular_error
        self.previous_linear_error = linear_error
        self.previous_time = current_time

        # Logs PRÉCIS pour le débogage
        rospy.loginfo(f"🎯 PID PRÉCIS: Angular P={angular_p:.4f} I={angular_i:.4f} D={angular_d:.4f} = {angular_command:.4f}")
        rospy.loginfo(f"🎯 PID PRÉCIS: Linear P={linear_p:.4f} I={linear_i:.4f} D={linear_d:.4f} = {linear_command:.4f}")

        return angular_command, linear_command

    def apply_differential_control(self, angular_command, linear_command):
        """
        COMMANDE DIFFÉRENTIELLE PROFESSIONNELLE
        Convertit les commandes angulaire/linéaire en commandes moteur
        """
        # Calcul des vitesses moteur différentielles
        right_motor = self.base_speed + linear_command - angular_command
        left_motor = self.base_speed + linear_command + angular_command

        # Saturation des moteurs
        right_motor = max(min(right_motor, self.max_torque), self.min_torque)
        left_motor = max(min(left_motor, self.max_torque), self.min_torque)

        rospy.loginfo(f"PID Control: Angular={angular_command:.3f}, Linear={linear_command:.3f}")
        rospy.loginfo(f"Motors: Right={right_motor:.3f}, Left={left_motor:.3f}")

        return right_motor, left_motor

    def regulate_towards_waste(self, left_count, right_count, middle_count, bottom_middle_count, upper_left_count=0, upper_right_count=0):
        """
        RÉGULATION PROFESSIONNELLE POUR MAINTENIR LE DÉCHET EN MIDDLE
        Objectif: Orienter le bateau pour que le déchet soit dans la zone MIDDLE
        6 ZONES: Left, Right, Upper left, Middle, Upper right, Bottom middle
        """

        # OBJECTIF PRINCIPAL: Déchet en Bottom Middle = CONTINUER L'AVANCEMENT POUR RAMASSER
        if bottom_middle_count >= 1:
            rospy.loginfo("🎯 RAMASSAGE: Déchet en Bottom Middle - AVANCEMENT POUR COLLECTE!")

            # Ne pas s'arrêter immédiatement, continuer d'avancer pour ramasser
            # Calculer l'erreur pour maintenir le centrage et avancer
            angular_error, linear_error = self.calculate_waste_position_error(
                left_count, right_count, middle_count, bottom_middle_count, upper_left_count, upper_right_count
            )

            # Appliquer la régulation PID pour maintenir le centrage et avancer
            angular_command, linear_command = self.pid_controller(angular_error, linear_error)
            right_motor, left_motor = self.apply_differential_control(angular_command, linear_command)

            # Appliquer les commandes aux moteurs
            self.apply_torque(self.fan_right, right_motor)
            self.apply_torque(self.fan_left, left_motor)

            rospy.loginfo(f"🎯 RAMASSAGE ACTIF: Angular={angular_error:.2f}, Linear={linear_error:.2f}")
            rospy.loginfo(f"⚙️ MOTEURS RAMASSAGE: Droite={right_motor:.3f}, Gauche={left_motor:.3f}")

            self.regulation_active = True

            # Compter la collecte après un certain temps d'avancement
            # (vous pouvez ajuster cette logique selon vos besoins)
            return False

        # STRATÉGIE: Calculer l'erreur pour amener le déchet vers MIDDLE
        angular_error, linear_error = self.calculate_waste_position_error(
            left_count, right_count, middle_count, bottom_middle_count, upper_left_count, upper_right_count
        )

        # RÉGULATION PID PROFESSIONNELLE
        angular_command, linear_command = self.pid_controller(angular_error, linear_error)

        # APPLICATION DE LA COMMANDE DIFFÉRENTIELLE
        right_motor, left_motor = self.apply_differential_control(angular_command, linear_command)

        # APPLIQUER LES COMMANDES AUX MOTEURS
        self.apply_torque(self.fan_right, right_motor)
        self.apply_torque(self.fan_left, left_motor)

        # AFFICHAGE DU STATUT D'ORIENTATION VERS LE DÉCHET
        rospy.loginfo(f" ORIENTATION VERS DÉCHET: Angular={angular_error:.2f}, Linear={linear_error:.2f}")
        rospy.loginfo(f" COMMANDES MOTEUR: Droite={right_motor:.3f}, Gauche={left_motor:.3f}")

        # DÉTERMINER LE TYPE D'ORIENTATION EN COURS
        waste_detected = (left_count > 0 or right_count > 0 or middle_count > 0)

        if waste_detected:
            if left_count >= 1 and right_count == 0:
                if middle_count >= 1:
                    rospy.loginfo("ORIENTATION: Déchet à gauche + centre - rotation modérée VERS LA GAUCHE")
                else:
                    rospy.loginfo(" ORIENTATION: Déchet à gauche - rotation forte VERS LA GAUCHE")
            elif right_count >= 1 and left_count == 0:
                if middle_count >= 1:
                    rospy.loginfo("ORIENTATION: Déchet à droite + centre - rotation modérée VERS LA DROITE")
                else:
                    rospy.loginfo("ORIENTATION: Déchet à droite - rotation forte VERS LA DROITE")
            elif middle_count >= 1:
                rospy.loginfo("ORIENTATION: Déchet centré - guidage direct vers Bottom Middle")
            elif left_count >= 1 and right_count >= 1:
                rospy.loginfo(" ORIENTATION: Déchet des deux côtés - maintien du cap central")
        else:
            rospy.loginfo(" RECHERCHE: Aucun déchet détecté - avancement de recherche")

        self.regulation_active = True
        return False

    def waste_detection_callback(self, msg):
        # Initialize counts for the 6 zones exactly
        middle_count = 0
        bottom_middle_count = 0
        left_count = 0
        right_count = 0
        upper_left_count = 0
        upper_right_count = 0

        for section in msg.sections:
            if section.section == "Middle":
                middle_count = section.count
            elif section.section == "Bottom middle":
                bottom_middle_count = section.count
            elif section.section == "Left":
                left_count = section.count
            elif section.section == "Right":
                right_count = section.count
            elif section.section == "Upper left":
                upper_left_count = section.count
            elif section.section == "Upper right":
                upper_right_count = section.count

        rospy.loginfo(f"🎯 DÉTECTION 6 ZONES: Left={left_count}, Right={right_count}, Middle={middle_count}, Bottom Middle={bottom_middle_count}")
        rospy.loginfo(f"🎯 ZONES UPPER: Upper Left={upper_left_count}, Upper Right={upper_right_count}")

        # SYSTÈME DE RÉGULATION INTELLIGENT - Focus sur maintenir en MIDDLE
        collected = self.regulate_towards_waste(left_count, right_count, middle_count, bottom_middle_count, upper_left_count, upper_right_count)

        if collected:
            rospy.loginfo(f"Total waste collected: {self.collection_count}")
            # Pause courte après collecte avant de reprendre la recherche
            rospy.sleep(1.0)

    def run(self):
        rospy.spin()

if __name__ == '__main__':
    try:
        node = ProfessionalWasteCollector()
        node.run()
    except rospy.ROSInterruptException:
        pass

























































