#!/usr/bin/env python3

import rospy
import math
import time
from asv_wave_sim_gazebo.msg import WasteDetection
from gazebo_msgs.srv import ApplyBodyWrench, ApplyBodyWrenchRequest, BodyRequest

class ProfessionalWasteCollector:
    def __init__(self):
        rospy.init_node('professional_waste_collector', anonymous=True)

        # Wait for Gazebo services
        try:
            rospy.wait_for_service('/gazebo/apply_body_wrench', timeout=10)
            rospy.wait_for_service('/gazebo/clear_body_wrenches', timeout=10)
        except rospy.ROSException as e:
            rospy.logerr(f"Gazebo services not available: {e}")
            return

        self.apply_wrench = rospy.ServiceProxy('/gazebo/apply_body_wrench', ApplyBodyWrench)
        self.clear_wrench = rospy.ServiceProxy('/gazebo/clear_body_wrenches', BodyRequest)

        # PARAMÈTRES PID POUR LA RÉGULATION CONTINUE
        self.kp_angular = 0.4    # Gain proportionnel augmenté pour des corrections plus fortes
        self.ki_angular = 0.05   # Gain intégral pour corriger l'erreur accumulée
        self.kd_angular = 0.1    # Gain dérivé pour stabiliser le mouvement

        self.kp_linear = 0.2     # Gain proportionnel pour l'avancement
        self.ki_linear = 0.02    # Gain intégral pour l'avancement
        self.kd_linear = 0.05    # Gain dérivé pour l'avancement

        # VITESSES DE RÉFÉRENCE POUR GAZEBO + ROS NOETIC
        self.search_speed = 0.5          # Vitesse de recherche (aucun déchet)
        self.approach_speed = 0.15       # Vitesse LENTE pour approcher le déchet
        self.upper_middle_speed = 0.2    # Vitesse LENTE pour maintenir en Upper Middle
        self.ramassage_speed = 0.4       # Vitesse pour ramassage final en Bottom Middle
        self.regulation_speed = 0.3      # Vitesse de référence pour la régulation

        # Seuils de régulation pour le centrage
        self.centering_threshold = 0.02  # Seuil très strict pour le centrage
        self.min_detection_ratio = 0.1   # Ratio minimum pour considérer une détection significative
        self.upper_centering_threshold = 0.02  # Seuil très strict pour le centrage dans Upper Middle
        self.upper_centering_time = 0.0  # Temps passé en phase de centrage Upper Middle
        self.min_upper_centering_time = 3.0  # Temps minimum augmenté pour une meilleure stabilisation
        self.min_upper_detection_ratio = 0.8  # Ratio minimum de détection dans Upper Middle (80%)

        # Limites de commande
        self.max_torque = 0.20
        self.min_torque = -0.20
        self.base_speed = -0.30

        # Variables d'état du régulateur
        self.angular_error_integral = 0.0
        self.linear_error_integral = 0.0
        self.previous_angular_error = 0.0
        self.previous_linear_error = 0.0
        self.previous_time = time.time()

        # Identification des ventilateurs
        self.fan_right = "boatcleaningc::fandroit"
        self.fan_left = "boatcleaningc::fangauche"

        # État du système
        self.collection_count = 0
        self.regulation_mode = "SEARCH"  # SEARCH, ORIENT, APPROACH, COLLECT
        self.target_position = {"x": 0, "y": 0}  # Position cible du déchet
        self.regulation_active = False

        # État de navigation : 'search', 'upper_centering', ou 'bottom_collection'
        self.navigation_phase = 'search'  # Phase initiale : recherche

        # Variables manquantes pour compatibilité
        self.strong_turn = -0.15
        self.medium_turn = -0.10
        self.forward_speed = -0.08

        # Seuils de précision pour la détection
        self.precision_threshold = 0.05  # Seuil de précision pour le centrage
        self.approach_threshold = 0.1    # Seuil pour l'approche progressive

        # Subscribe to waste detection
        rospy.Subscriber('/waste_detection', WasteDetection, self.waste_detection_callback)
        rospy.loginfo("Professional Waste Collector with PID Regulation started!")

    def apply_torque(self, link_name, torque):
        try:
            self.clear_wrench(link_name)
            if abs(torque) > 0.001:
                req = ApplyBodyWrenchRequest()
                req.body_name = link_name
                req.reference_frame = "world"
                req.wrench.torque.x = torque
                req.duration = rospy.Duration(-1)
                self.apply_wrench(req)
                rospy.loginfo(f"Applied torque {torque} on {link_name}")
            else:
                rospy.loginfo(f"Cleared torque on {link_name}")
        except rospy.ServiceException as e:
            rospy.logerr(f"Failed to apply torque on {link_name}: {e}")

    def stop_fans(self):
        self.apply_torque(self.fan_right, 0.0)
        self.apply_torque(self.fan_left, 0.0)
        rospy.loginfo("Boat stopped.")

    def move_forward_slowly(self):
        """Move forward slowly for search and approach"""
        self.apply_torque(self.fan_right, self.base_speed)
        self.apply_torque(self.fan_left, self.base_speed)
        rospy.loginfo("Boat moving forward slowly.")

    def calculate_waste_position_error(self, left_count, right_count, middle_count, bottom_middle_count, left_middle_count=0, right_middle_count=0):
        """
        🎯 STRATÉGIE SIMPLE EN DEUX PHASES :
        Phase 1: Recherche à vitesse modérée quand aucun déchet
        Phase 2: Guider TOUS les déchets vers la zone MIDDLE (objectif principal)
        """
        # Déterminer la phase de navigation (inclure TOUTES les zones)
        waste_detected = (left_count > 0 or right_count > 0 or middle_count > 0 or bottom_middle_count > 0 or
                         left_middle_count > 0 or right_middle_count > 0)

        if waste_detected:
            # PHASE 2: COLLECTE - Déchet détecté, passer en mode collecte
            if self.navigation_phase == 'search':
                self.navigation_phase = 'collection'
                rospy.loginfo("🔄 CHANGEMENT DE PHASE: RECHERCHE → COLLECTE (déchet détecté)")
        else:
            # PHASE 1: RECHERCHE - Aucun déchet, passer en mode recherche
            if self.navigation_phase == 'collection':
                self.navigation_phase = 'search'
                rospy.loginfo("🔄 CHANGEMENT DE PHASE: COLLECTE → RECHERCHE (aucun déchet)")

        # 🎯 PRIORITÉ 1: DÉCHET EN MIDDLE - MAINTENIR DANS LA ZONE MIDDLE
        if middle_count > 0:
            angular_error = 0.0
            linear_error = self.upper_middle_speed  # Vitesse lente pour maintenir en Middle
            rospy.loginfo("🎯 OBJECTIF ATTEINT: Déchet en MIDDLE - maintien dans la zone de collecte!")
            return angular_error, linear_error

        # 🎯 PRIORITÉ 2: DÉCHET EN BOTTOM MIDDLE - RAMASSAGE FINAL
        if bottom_middle_count > 0:
            angular_error = 0.0
            linear_error = self.ramassage_speed  # AVANCEMENT FORT pour ramasser le déchet
            rospy.loginfo("🎯 RAMASSAGE: Déchet en Bottom Middle - AVANCEMENT POUR COLLECTE!")
            return angular_error, linear_error

        # 🎯 GESTION DES DEUX PHASES DE NAVIGATION
        angular_error = 0.0
        linear_error = 0.0

        if self.navigation_phase == 'search':
            # ===== PHASE 1: RECHERCHE RAPIDE =====
            angular_error = 0.0  # Maintenir le cap droit
            linear_error = self.search_speed  # Vitesse modérée pour recherche
            rospy.loginfo(f"🔍 PHASE RECHERCHE: Avancement rapide à {self.search_speed} - aucun déchet détecté")

        elif self.navigation_phase == 'collection':
            # ===== PHASE 2: COLLECTE AVEC PETITE VITESSE =====
            # Déchet détecté - Aller vers le déchet avec vitesse LENTE et régulation PID
            rospy.loginfo("🎯 PHASE COLLECTE: Déchet détecté - approche LENTE avec régulation!")

            # STRATÉGIE: Guider TOUS les déchets vers la zone MIDDLE
            if left_count > 0 or left_middle_count > 0:
                # Déchet à GAUCHE ou LEFT MIDDLE - ROTATION à gauche pour guider vers MIDDLE
                if middle_count > 0:
                    angular_error = 0.05   # MICRO-ajustement très doux - déchet déjà en Middle
                    linear_error = self.upper_middle_speed  # Vitesse lente pour maintenir en Middle
                    rospy.loginfo("🎯 MAINTIEN: Déchet gauche + Middle - MAINTIEN EN MIDDLE")
                else:
                    angular_error = 0.15   # Rotation douce vers Middle
                    linear_error = self.approach_speed  # Vitesse très lente pour approcher
                    rospy.loginfo("🔄 GUIDAGE: Déchet à gauche - rotation vers MIDDLE")

            elif right_count > 0 or right_middle_count > 0:
                # Déchet à DROITE ou RIGHT MIDDLE - ROTATION à droite pour guider vers MIDDLE
                if middle_count > 0:
                    angular_error = -0.05  # MICRO-ajustement très doux - déchet déjà en Middle
                    linear_error = self.upper_middle_speed  # Vitesse lente pour maintenir en Middle
                    rospy.loginfo("🎯 MAINTIEN: Déchet droite + Middle - MAINTIEN EN MIDDLE")
                else:
                    angular_error = -0.15  # Rotation douce vers Middle
                    linear_error = self.approach_speed  # Vitesse très lente pour approcher
                    rospy.loginfo("🔄 GUIDAGE: Déchet à droite - rotation vers MIDDLE")

            elif middle_count > 0:
                # 🎯 DÉCHET EN MIDDLE - OBJECTIF ATTEINT, MAINTENIR LA POSITION
                angular_error = 0.0  # Maintenir le cap droit
                linear_error = self.upper_middle_speed  # Vitesse lente pour maintenir en Middle
                rospy.loginfo("🎯 SUCCÈS: Déchet en MIDDLE - MAINTIEN DANS LA ZONE DE COLLECTE!")

            else:
                # Sécurité - déchet détecté mais pas localisé précisément
                angular_error = 0.0
                linear_error = self.approach_speed  # Vitesse très lente par sécurité
                rospy.loginfo("⚠️ SÉCURITÉ: Déchet détecté - avancement très lent")

        rospy.loginfo(f"🎯 COMMANDES [{self.navigation_phase.upper()}]: Angular={angular_error:.2f}, Linear={linear_error:.2f}")
        return angular_error, linear_error

    def pid_controller(self, angular_error, linear_error):
        """
        RÉGULATEUR PID CONTINU
        Calcule les commandes moteur basées sur les erreurs avec régulation continue
        """
        current_time = time.time()
        dt = current_time - self.previous_time

        if dt <= 0:
            dt = 0.01  # Éviter division par zéro

        # === RÉGULATION ANGULAIRE CONTINUE ===
        # Terme proportionnel
        angular_p = self.kp_angular * angular_error

        # Terme intégral (accumulation de l'erreur)
        self.angular_error_integral += angular_error * dt
        # Limitation de l'accumulation pour éviter le windup
        self.angular_error_integral = max(min(self.angular_error_integral, 1.0), -1.0)
        angular_i = self.ki_angular * self.angular_error_integral

        # Terme dérivé (vitesse de changement de l'erreur)
        angular_derivative = (angular_error - self.previous_angular_error) / dt
        angular_d = self.kd_angular * angular_derivative

        # Commande angulaire totale
        angular_command = angular_p + angular_i + angular_d

        # === RÉGULATION LINÉAIRE CONTINUE ===
        # Terme proportionnel
        linear_p = self.kp_linear * linear_error

        # Terme intégral
        self.linear_error_integral += linear_error * dt
        # Limitation de l'accumulation
        self.linear_error_integral = max(min(self.linear_error_integral, 1.0), -1.0)
        linear_i = self.ki_linear * self.linear_error_integral

        # Terme dérivé
        linear_derivative = (linear_error - self.previous_linear_error) / dt
        linear_d = self.kd_linear * linear_derivative

        # Commande linéaire totale
        linear_command = linear_p + linear_i + linear_d

        # Saturation des commandes
        angular_command = max(min(angular_command, self.max_torque), self.min_torque)
        linear_command = max(min(linear_command, self.max_torque), self.min_torque)

        # Mise à jour des variables d'état
        self.previous_angular_error = angular_error
        self.previous_linear_error = linear_error
        self.previous_time = current_time

        rospy.loginfo(f"PID: Angular P={angular_p:.3f} I={angular_i:.3f} D={angular_d:.3f}")
        rospy.loginfo(f"PID: Linear P={linear_p:.3f} I={linear_i:.3f} D={linear_d:.3f}")

        return angular_command, linear_command

    def apply_differential_control(self, angular_command, linear_command):
        """
        COMMANDE DIFFÉRENTIELLE PROFESSIONNELLE
        Convertit les commandes angulaire/linéaire en commandes moteur
        """
        # Calcul des vitesses moteur différentielles
        right_motor = self.base_speed + linear_command - angular_command
        left_motor = self.base_speed + linear_command + angular_command

        # Saturation des moteurs
        right_motor = max(min(right_motor, self.max_torque), self.min_torque)
        left_motor = max(min(left_motor, self.max_torque), self.min_torque)

        rospy.loginfo(f"PID Control: Angular={angular_command:.3f}, Linear={linear_command:.3f}")
        rospy.loginfo(f"Motors: Right={right_motor:.3f}, Left={left_motor:.3f}")

        return right_motor, left_motor

    def regulate_towards_waste(self, left_count, right_count, middle_count, bottom_middle_count, left_middle_count=0, right_middle_count=0):
        """
        RÉGULATION PROFESSIONNELLE POUR MAINTENIR LE DÉCHET EN MIDDLE
        Objectif: Orienter le bateau pour que le déchet soit dans la zone MIDDLE
        """

        # OBJECTIF PRINCIPAL: Déchet en Bottom Middle = CONTINUER L'AVANCEMENT POUR RAMASSER
        if bottom_middle_count >= 1:
            rospy.loginfo("🎯 RAMASSAGE: Déchet en Bottom Middle - AVANCEMENT POUR COLLECTE!")

            # Ne pas s'arrêter immédiatement, continuer d'avancer pour ramasser
            # Calculer l'erreur pour maintenir le centrage et avancer
            angular_error, linear_error = self.calculate_waste_position_error(
                left_count, right_count, middle_count, bottom_middle_count, left_middle_count, right_middle_count
            )

            # Appliquer la régulation PID pour maintenir le centrage et avancer
            angular_command, linear_command = self.pid_controller(angular_error, linear_error)
            right_motor, left_motor = self.apply_differential_control(angular_command, linear_command)

            # Appliquer les commandes aux moteurs
            self.apply_torque(self.fan_right, right_motor)
            self.apply_torque(self.fan_left, left_motor)

            rospy.loginfo(f"🎯 RAMASSAGE ACTIF: Angular={angular_error:.2f}, Linear={linear_error:.2f}")
            rospy.loginfo(f"⚙️ MOTEURS RAMASSAGE: Droite={right_motor:.3f}, Gauche={left_motor:.3f}")

            self.regulation_active = True

            # Compter la collecte après un certain temps d'avancement
            # (vous pouvez ajuster cette logique selon vos besoins)
            return False

        # STRATÉGIE: Calculer l'erreur pour amener le déchet vers MIDDLE
        angular_error, linear_error = self.calculate_waste_position_error(
            left_count, right_count, middle_count, bottom_middle_count, left_middle_count, right_middle_count
        )

        # RÉGULATION PID PROFESSIONNELLE
        angular_command, linear_command = self.pid_controller(angular_error, linear_error)

        # APPLICATION DE LA COMMANDE DIFFÉRENTIELLE
        right_motor, left_motor = self.apply_differential_control(angular_command, linear_command)

        # APPLIQUER LES COMMANDES AUX MOTEURS
        self.apply_torque(self.fan_right, right_motor)
        self.apply_torque(self.fan_left, left_motor)

        # AFFICHAGE DU STATUT D'ORIENTATION VERS LE DÉCHET
        rospy.loginfo(f" ORIENTATION VERS DÉCHET: Angular={angular_error:.2f}, Linear={linear_error:.2f}")
        rospy.loginfo(f" COMMANDES MOTEUR: Droite={right_motor:.3f}, Gauche={left_motor:.3f}")

        # DÉTERMINER LE TYPE D'ORIENTATION EN COURS
        waste_detected = (left_count > 0 or right_count > 0 or middle_count > 0)

        if waste_detected:
            if left_count >= 1 and right_count == 0:
                if middle_count >= 1:
                    rospy.loginfo("ORIENTATION: Déchet à gauche + centre - rotation modérée VERS LA GAUCHE")
                else:
                    rospy.loginfo(" ORIENTATION: Déchet à gauche - rotation forte VERS LA GAUCHE")
            elif right_count >= 1 and left_count == 0:
                if middle_count >= 1:
                    rospy.loginfo("ORIENTATION: Déchet à droite + centre - rotation modérée VERS LA DROITE")
                else:
                    rospy.loginfo("ORIENTATION: Déchet à droite - rotation forte VERS LA DROITE")
            elif middle_count >= 1:
                rospy.loginfo("ORIENTATION: Déchet centré - guidage direct vers Bottom Middle")
            elif left_count >= 1 and right_count >= 1:
                rospy.loginfo(" ORIENTATION: Déchet des deux côtés - maintien du cap central")
        else:
            rospy.loginfo(" RECHERCHE: Aucun déchet détecté - avancement de recherche")

        self.regulation_active = True
        return False

    def waste_detection_callback(self, msg):
        # Initialize counts for ALL sections (8 zones)
        middle_count = 0
        bottom_middle_count = 0
        left_count = 0
        right_count = 0
        left_middle_count = 0
        right_middle_count = 0
        upper_left_count = 0
        upper_right_count = 0

        for section in msg.sections:
            if section.section == "Middle":
                middle_count = section.count
            elif section.section == "Bottom Middle":
                bottom_middle_count = section.count
            elif section.section == "Left":
                left_count = section.count
            elif section.section == "Right":
                right_count = section.count
            elif section.section == "Left Middle":
                left_middle_count = section.count
            elif section.section == "Right Middle":
                right_middle_count = section.count
            elif section.section == "Upper left":
                upper_left_count = section.count
            elif section.section == "Upper right":
                upper_right_count = section.count

        rospy.loginfo(f"🎯 DÉTECTION COMPLÈTE: Left={left_count}, Right={right_count}, Middle={middle_count}, Bottom Middle={bottom_middle_count}")
        rospy.loginfo(f"🎯 ZONES SUPPLÉMENTAIRES: Left Middle={left_middle_count}, Right Middle={right_middle_count}, Upper Left={upper_left_count}, Upper Right={upper_right_count}")

        # SYSTÈME DE RÉGULATION INTELLIGENT - Focus sur maintenir en MIDDLE
        collected = self.regulate_towards_waste(left_count, right_count, middle_count, bottom_middle_count, left_middle_count, right_middle_count)

        if collected:
            rospy.loginfo(f"Total waste collected: {self.collection_count}")
            # Pause courte après collecte avant de reprendre la recherche
            rospy.sleep(1.0)

    def run(self):
        rospy.spin()

if __name__ == '__main__':
    try:
        node = ProfessionalWasteCollector()
        node.run()
    except rospy.ROSInterruptException:
        pass




















































